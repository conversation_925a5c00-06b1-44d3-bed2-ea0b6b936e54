// Test file to verify data transformation
import { transformMetricToChartData, getMetricByName } from "./utils/transformTrafficData";

const mockData = {
  period: {
    start_date: "2025-06-19",
    end_date: "2025-07-19",
    days_count: 31,
  },
  metrics: {
    total_users: {
      total_value: 8096,
      traffic_sources: {
        organic: { value: 7591, percentage: 93.8 },
        paid: { value: 0, percentage: 0 },
        direct: { value: 386, percentage: 4.8 },
        social: { value: 0, percentage: 0 },
        referral: { value: 22, percentage: 0.3 },
        email: { value: 0, percentage: 0 },
        unassigned: { value: 97, percentage: 1.2 },
      },
    },
    conversions: {
      total_value: 0,
      traffic_sources: {
        organic: { value: 0, percentage: 0 },
        paid: { value: 0, percentage: 0 },
        direct: { value: 0, percentage: 0 },
        social: { value: 0, percentage: 0 },
        referral: { value: 0, percentage: 0 },
        email: { value: 0, percentage: 0 },
        unassigned: { value: 0, percentage: 0 },
      },
    },
  },
};

// Test Total Users transformation
const totalUsersMetric = getMetricByName(mockData, "Total Users");
if (totalUsersMetric) {
  const chartData = transformMetricToChartData(totalUsersMetric, "Total Users");
  console.log("Total Users Chart Data:", JSON.stringify(chartData, null, 2));
}

// Test Conversions transformation (all zeros)
const conversionsMetric = getMetricByName(mockData, "Conversions");
if (conversionsMetric) {
  const chartData = transformMetricToChartData(conversionsMetric, "Conversions");
  console.log("Conversions Chart Data:", JSON.stringify(chartData, null, 2));
}

export {};
