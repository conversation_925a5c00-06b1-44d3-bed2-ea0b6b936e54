export type TBar = {
  value: number;
  color: string;
  actualValue?: number; // Optional actual value for tooltip display
  label?: string; // Optional label for tooltip display (e.g., "Previous", "Current")
};

type isLoading = {
  label?: string;
  totalValue?: number;
  bars?: TBar[];
  className?: string;
  percentageLabel?: string;
  isLoading: true;
};

type isLoaded = {
  label: string;
  totalValue: number;
  bars: TBar[];
  className?: string;
  percentageLabel?: string;
  isLoading?: false;
};

export type THorizontalBar = isLoaded | isLoading;
