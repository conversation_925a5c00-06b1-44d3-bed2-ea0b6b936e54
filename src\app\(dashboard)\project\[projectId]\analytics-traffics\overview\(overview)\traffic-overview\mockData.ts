import type { TrafficOverviewResponse } from "../../../types/HorizontalBars.types";

/**
 * Mock data for testing the TrafficOverview component
 * This matches the structure provided in the requirements
 */
export const mockTrafficOverviewData: TrafficOverviewResponse = {
  status: "success",
  project_id: "d8f2b06c-de6d-4181-b9d6-ff9b75c59810",
  data: {
    period: {
      start_date: "2025-06-19",
      end_date: "2025-07-19",
      days_count: 31,
    },
    metrics: {
      total_users: {
        total_value: 8096,
        traffic_sources: {
          organic: {
            value: 7591,
            percentage: 93.8,
          },
          paid: {
            value: 0,
            percentage: 0,
          },
          direct: {
            value: 386,
            percentage: 4.8,
          },
          social: {
            value: 0,
            percentage: 0,
          },
          referral: {
            value: 22,
            percentage: 0.3,
          },
          email: {
            value: 0,
            percentage: 0,
          },
          unassigned: {
            value: 97,
            percentage: 1.2,
          },
        },
      },
      new_users: {
        total_value: 7740,
        traffic_sources: {
          organic: {
            value: 7323,
            percentage: 94.6,
          },
          paid: {
            value: 0,
            percentage: 0,
          },
          direct: {
            value: 377,
            percentage: 4.9,
          },
          social: {
            value: 0,
            percentage: 0,
          },
          referral: {
            value: 20,
            percentage: 0.3,
          },
          email: {
            value: 0,
            percentage: 0,
          },
          unassigned: {
            value: 20,
            percentage: 0.3,
          },
        },
      },
      sessions: {
        total_value: 9746,
        traffic_sources: {
          organic: {
            value: 9164,
            percentage: 94,
          },
          paid: {
            value: 0,
            percentage: 0,
          },
          direct: {
            value: 451,
            percentage: 4.6,
          },
          social: {
            value: 0,
            percentage: 0,
          },
          referral: {
            value: 32,
            percentage: 0.3,
          },
          email: {
            value: 0,
            percentage: 0,
          },
          unassigned: {
            value: 99,
            percentage: 1,
          },
        },
      },
      active_users: {
        total_value: 7939,
        traffic_sources: {
          organic: {
            value: 7443,
            percentage: 93.8,
          },
          paid: {
            value: 0,
            percentage: 0,
          },
          direct: {
            value: 378,
            percentage: 4.8,
          },
          social: {
            value: 0,
            percentage: 0,
          },
          referral: {
            value: 21,
            percentage: 0.3,
          },
          email: {
            value: 0,
            percentage: 0,
          },
          unassigned: {
            value: 97,
            percentage: 1.2,
          },
        },
      },
      page_views: {
        total_value: 12739,
        traffic_sources: {
          organic: {
            value: 12005,
            percentage: 94.2,
          },
          paid: {
            value: 0,
            percentage: 0,
          },
          direct: {
            value: 623,
            percentage: 4.9,
          },
          social: {
            value: 0,
            percentage: 0,
          },
          referral: {
            value: 30,
            percentage: 0.2,
          },
          email: {
            value: 0,
            percentage: 0,
          },
          unassigned: {
            value: 81,
            percentage: 0.6,
          },
        },
      },
      event_count: {
        total_value: 39363,
        traffic_sources: {
          organic: {
            value: 37283,
            percentage: 94.7,
          },
          paid: {
            value: 0,
            percentage: 0,
          },
          direct: {
            value: 1739,
            percentage: 4.4,
          },
          social: {
            value: 0,
            percentage: 0,
          },
          referral: {
            value: 105,
            percentage: 0.3,
          },
          email: {
            value: 0,
            percentage: 0,
          },
          unassigned: {
            value: 236,
            percentage: 0.6,
          },
        },
      },
      conversions: {
        total_value: 0,
        traffic_sources: {
          organic: {
            value: 0,
            percentage: 0,
          },
          paid: {
            value: 0,
            percentage: 0,
          },
          direct: {
            value: 0,
            percentage: 0,
          },
          social: {
            value: 0,
            percentage: 0,
          },
          referral: {
            value: 0,
            percentage: 0,
          },
          email: {
            value: 0,
            percentage: 0,
          },
          unassigned: {
            value: 0,
            percentage: 0,
          },
        },
      },
    },
  },
  last_sync: "2025-07-13T12:44:59.567982Z",
};
