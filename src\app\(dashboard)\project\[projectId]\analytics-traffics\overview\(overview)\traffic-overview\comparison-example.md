# Traffic Overview Comparison Example

## How the Comparison Chart Works

The chart now displays two periods side by side for each traffic source:

### Example: Total Users Comparison

**Previous Period (May 19 - Jun 18):** 6,500 total users
**Current Period (Jun 19 - Jul 19):** 8,096 total users

### Visual Representation

Each traffic source shows both periods in the same bar:

#### Organic Traffic
- **Yellow bar (#FFCD29)**: 5,850 users (previous period)
- **Purple bar**: 7,591 users (current period)
- Shows growth from 90% to 93.8% of total traffic

#### Direct Traffic  
- **Yellow bar (#FFCD29)**: 520 users (previous period)
- **Purple bar**: 386 users (current period)
- Shows decrease from 8% to 4.8% of total traffic

#### Referral Traffic
- **Yellow bar (#FFCD29)**: 65 users (previous period)  
- **Purple bar**: 22 users (current period)
- Shows decrease from 1% to 0.3% of total traffic

#### Unassigned Traffic
- **Yellow bar (#FFCD29)**: 65 users (previous period)
- **Purple bar**: 97 users (current period)
- Shows increase from 1% to 1.2% of total traffic

## Color Coding

- **Yellow (#FFCD29)**: Previous period data
- **Purple**: Current period data
- **Gray**: No data placeholder

## Benefits

1. **Easy Comparison**: See both periods at a glance
2. **Growth Visualization**: Longer purple bars show growth
3. **Trend Analysis**: Compare traffic source performance
4. **Proportional Scaling**: Bars scale to the maximum value across both periods

## Data Structure

The component now uses `ComparisonTrafficOverviewData` which includes:
- `current`: Current period metrics
- `previous`: Previous period metrics

Both periods are displayed simultaneously in each horizontal bar for easy comparison.
