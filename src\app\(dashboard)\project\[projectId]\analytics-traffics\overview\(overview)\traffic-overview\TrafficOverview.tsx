import React, { useState, useMemo } from "react";

/* ================================ SKELETON ================================ */
import Skeleton from "react-loading-skeleton";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import CardTab from "@/components/ui/card-tab/CardTab";
import OrganicTraffic from "./_components/OrganicTraffic";
import { Button } from "@/components/ui/button";
import DateRange from "../../../_components/date-range/DateRange";
import NoData from "../../../analytic-insight/_components/NoData";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ================================== UTILS ================================= */
import {
  transformMetricToChartData,
  getMetricByName,
  formatMetricValue,
  calculatePercentageChange,
} from "./utils/transformTrafficData";

const TrafficOverViewSkeleton = () => {
  return (
    <div className="w-[90%] mx-8 p-2 space-y-2 border-l border-b">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index}>
          <Skeleton width={80} height={15} />
          <Skeleton width={`${Math.floor(Math.random() * 101)}%`} />
        </div>
      ))}
    </div>
  );
};

/* ================================ API CALLS =============================== */
// import useTrafficOverview from "./TrafficOverview.hook";
import Link from "next/link";
import type { TrafficOverviewResponse } from "../../../types/HorizontalBars.types";

/* ========================================================================== */
const TrafficOverview = () => {
  /* ================================ CONSTANTS =============================== */
  const { themeColor } = useAppThemeColor();

  // Mock data - disable API call and use provided data
  const trafficData: TrafficOverviewResponse = useMemo(
    () => ({
      status: "success",
      project_id: "d8f2b06c-de6d-4181-b9d6-ff9b75c59810",
      data: {
        period: {
          start_date: "2025-06-19",
          end_date: "2025-07-19",
          days_count: 31,
        },
        metrics: {
          total_users: {
            total_value: 8096,
            traffic_sources: {
              organic: { value: 7591, percentage: 93.8 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 386, percentage: 4.8 },
              social: { value: 0, percentage: 0 },
              referral: { value: 22, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 97, percentage: 1.2 },
            },
          },
          new_users: {
            total_value: 7740,
            traffic_sources: {
              organic: { value: 7323, percentage: 94.6 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 377, percentage: 4.9 },
              social: { value: 0, percentage: 0 },
              referral: { value: 20, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 20, percentage: 0.3 },
            },
          },
          sessions: {
            total_value: 9746,
            traffic_sources: {
              organic: { value: 9164, percentage: 94 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 451, percentage: 4.6 },
              social: { value: 0, percentage: 0 },
              referral: { value: 32, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 99, percentage: 1 },
            },
          },
          active_users: {
            total_value: 7939,
            traffic_sources: {
              organic: { value: 7443, percentage: 93.8 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 378, percentage: 4.8 },
              social: { value: 0, percentage: 0 },
              referral: { value: 21, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 97, percentage: 1.2 },
            },
          },
          page_views: {
            total_value: 12739,
            traffic_sources: {
              organic: { value: 12005, percentage: 94.2 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 623, percentage: 4.9 },
              social: { value: 0, percentage: 0 },
              referral: { value: 30, percentage: 0.2 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 81, percentage: 0.6 },
            },
          },
          event_count: {
            total_value: 39363,
            traffic_sources: {
              organic: { value: 37283, percentage: 94.7 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 1739, percentage: 4.4 },
              social: { value: 0, percentage: 0 },
              referral: { value: 105, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 236, percentage: 0.6 },
            },
          },
          conversions: {
            total_value: 0,
            traffic_sources: {
              organic: { value: 0, percentage: 0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 0, percentage: 0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 0, percentage: 0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 0, percentage: 0 },
            },
          },
        },
      },
      last_sync: "2025-07-13T12:44:59.567982Z",
    }),
    []
  );

  const barsDataIsLoading = false;
  const barsDataIsPending = false;
  const barsDataError = null;

  // Generate tabs from API data
  const trafficOverviewTabs = useMemo(() => {
    if (!trafficData?.data) {
      return [
        { id: 1, title: "Total Users", value: "0", changeValue: "+0.0" },
        { id: 2, title: "New Users", value: "0", changeValue: "+0.0" },
        { id: 3, title: "Sessions", value: "0", changeValue: "+0.0" },
        { id: 4, title: "Active Users", value: "0", changeValue: "+0.0" },
        { id: 5, title: "Views", value: "0", changeValue: "+0.0" },
        { id: 6, title: "Event Count", value: "0", changeValue: "+0.0" },
        { id: 7, title: "Conversions", value: "0", changeValue: "+0.0" },
      ];
    }

    const { metrics } = trafficData.data;
    return [
      {
        id: 1,
        title: "Total Users",
        value: formatMetricValue(metrics.total_users.total_value),
        changeValue: calculatePercentageChange(metrics.total_users.total_value),
      },
      {
        id: 2,
        title: "New Users",
        value: formatMetricValue(metrics.new_users.total_value),
        changeValue: calculatePercentageChange(metrics.new_users.total_value),
      },
      {
        id: 3,
        title: "Sessions",
        value: formatMetricValue(metrics.sessions.total_value),
        changeValue: calculatePercentageChange(metrics.sessions.total_value),
      },
      {
        id: 4,
        title: "Active Users",
        value: formatMetricValue(metrics.active_users.total_value),
        changeValue: calculatePercentageChange(
          metrics.active_users.total_value
        ),
      },
      {
        id: 5,
        title: "Views",
        value: formatMetricValue(metrics.page_views.total_value),
        changeValue: calculatePercentageChange(metrics.page_views.total_value),
      },
      {
        id: 6,
        title: "Event Count",
        value: formatMetricValue(metrics.event_count.total_value),
        changeValue: calculatePercentageChange(metrics.event_count.total_value),
      },
      {
        id: 7,
        title: "Conversions",
        value: formatMetricValue(metrics.conversions.total_value),
        changeValue: calculatePercentageChange(metrics.conversions.total_value),
      },
    ];
  }, [trafficData]);

  const [activeTab, setActiveTab] = useState(
    trafficOverviewTabs[0]?.title || ""
  );

  // Transform data for chart
  const chartData = useMemo(() => {
    if (!trafficData?.data) return null;

    const metric = getMetricByName(trafficData.data, activeTab);
    if (!metric) return null;

    return transformMetricToChartData(metric, activeTab);
  }, [trafficData, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (
    !trafficData &&
    !barsDataIsLoading &&
    !barsDataIsPending &&
    !barsDataError
  ) {
    return <NoData title="Audience Overview" />;
  }

  return (
    <Card className="space-y-4">
      <Title>Traffic Overview</Title>
      <DateRange />

      <div className="flex justify-between overflow-x-auto gap-1 h-fit text-nowrap">
        {trafficOverviewTabs.map(({ id, title, value, changeValue }) => (
          <CardTab
            key={id}
            title={title}
            value={value}
            changeValue={changeValue}
            className={`border-2 `}
            style={
              activeTab === title
                ? { borderColor: themeColor }
                : { borderColor: "transparent" }
            }
            onSelect={() => setActiveTab(title)}
          />
        ))}
      </div>
      <div>
        {barsDataIsLoading || barsDataIsPending ? (
          <TrafficOverViewSkeleton />
        ) : (
          chartData && <OrganicTraffic barsData={chartData} />
        )}
      </div>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link
          href={"/project/analytics-traffics/analytic-insight?tab=traffics"}
        >
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default TrafficOverview;
