import React, { useState, useMemo } from "react";

/* ================================ SKELETON ================================ */
import Skeleton from "react-loading-skeleton";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import CardTab from "@/components/ui/card-tab/CardTab";
import OrganicTraffic from "./_components/OrganicTraffic";
import { Button } from "@/components/ui/button";
import DateRange from "../../../_components/date-range/DateRange";
import NoData from "../../../analytic-insight/_components/NoData";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ================================== UTILS ================================= */
import {
  transformComparisonMetricToChartData,
  getMetricByName,
  formatMetricValue,
  calculatePercentageChange,
} from "./utils/transformTrafficData";

const TrafficOverViewSkeleton = () => {
  return (
    <div className="w-[90%] mx-8 p-2 space-y-2 border-l border-b">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index}>
          <Skeleton width={80} height={15} />
          <Skeleton width={`${Math.floor(Math.random() * 101)}%`} />
        </div>
      ))}
    </div>
  );
};

/* ================================ API CALLS =============================== */
// import useTrafficOverview from "./TrafficOverview.hook";
import Link from "next/link";
import type { TrafficOverviewResponse } from "../../../types/HorizontalBars.types";

/* ========================================================================== */
const TrafficOverview = () => {
  /* ================================ CONSTANTS =============================== */
  const { themeColor } = useAppThemeColor();

  // Mock comparison data - current and previous periods
  const comparisonData = useMemo(
    () => ({
      current: {
        period: {
          start_date: "2025-06-19",
          end_date: "2025-07-19",
          days_count: 31,
        },
        metrics: {
          total_users: {
            total_value: 8096,
            traffic_sources: {
              organic: { value: 7591, percentage: 93.8 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 386, percentage: 4.8 },
              social: { value: 0, percentage: 0 },
              referral: { value: 22, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 97, percentage: 1.2 },
            },
          },
          new_users: {
            total_value: 7740,
            traffic_sources: {
              organic: { value: 7323, percentage: 94.6 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 377, percentage: 4.9 },
              social: { value: 0, percentage: 0 },
              referral: { value: 20, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 20, percentage: 0.3 },
            },
          },
          sessions: {
            total_value: 9746,
            traffic_sources: {
              organic: { value: 9164, percentage: 94 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 451, percentage: 4.6 },
              social: { value: 0, percentage: 0 },
              referral: { value: 32, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 99, percentage: 1 },
            },
          },
          active_users: {
            total_value: 7939,
            traffic_sources: {
              organic: { value: 7443, percentage: 93.8 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 378, percentage: 4.8 },
              social: { value: 0, percentage: 0 },
              referral: { value: 21, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 97, percentage: 1.2 },
            },
          },
          page_views: {
            total_value: 12739,
            traffic_sources: {
              organic: { value: 12005, percentage: 94.2 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 623, percentage: 4.9 },
              social: { value: 0, percentage: 0 },
              referral: { value: 30, percentage: 0.2 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 81, percentage: 0.6 },
            },
          },
          event_count: {
            total_value: 39363,
            traffic_sources: {
              organic: { value: 37283, percentage: 94.7 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 1739, percentage: 4.4 },
              social: { value: 0, percentage: 0 },
              referral: { value: 105, percentage: 0.3 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 236, percentage: 0.6 },
            },
          },
          conversions: {
            total_value: 0,
            traffic_sources: {
              organic: { value: 0, percentage: 0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 0, percentage: 0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 0, percentage: 0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 0, percentage: 0 },
            },
          },
        },
      },
      previous: {
        period: {
          start_date: "2025-05-19",
          end_date: "2025-06-18",
          days_count: 31,
        },
        metrics: {
          total_users: {
            total_value: 6500, // Lower than current
            traffic_sources: {
              organic: { value: 5850, percentage: 90.0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 520, percentage: 8.0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 65, percentage: 1.0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 65, percentage: 1.0 },
            },
          },
          new_users: {
            total_value: 6200,
            traffic_sources: {
              organic: { value: 5580, percentage: 90.0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 496, percentage: 8.0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 62, percentage: 1.0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 62, percentage: 1.0 },
            },
          },
          sessions: {
            total_value: 7800,
            traffic_sources: {
              organic: { value: 7020, percentage: 90.0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 624, percentage: 8.0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 78, percentage: 1.0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 78, percentage: 1.0 },
            },
          },
          active_users: {
            total_value: 6350,
            traffic_sources: {
              organic: { value: 5715, percentage: 90.0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 508, percentage: 8.0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 64, percentage: 1.0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 63, percentage: 1.0 },
            },
          },
          page_views: {
            total_value: 10200,
            traffic_sources: {
              organic: { value: 9180, percentage: 90.0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 816, percentage: 8.0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 102, percentage: 1.0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 102, percentage: 1.0 },
            },
          },
          event_count: {
            total_value: 31500,
            traffic_sources: {
              organic: { value: 28350, percentage: 90.0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 2520, percentage: 8.0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 315, percentage: 1.0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 315, percentage: 1.0 },
            },
          },
          conversions: {
            total_value: 0,
            traffic_sources: {
              organic: { value: 0, percentage: 0 },
              paid: { value: 0, percentage: 0 },
              direct: { value: 0, percentage: 0 },
              social: { value: 0, percentage: 0 },
              referral: { value: 0, percentage: 0 },
              email: { value: 0, percentage: 0 },
              unassigned: { value: 0, percentage: 0 },
            },
          },
        },
      },
    }),
    []
  );

  // For backward compatibility, create trafficData from current period
  const trafficData: TrafficOverviewResponse = useMemo(
    () => ({
      status: "success",
      project_id: "d8f2b06c-de6d-4181-b9d6-ff9b75c59810",
      data: comparisonData.current,
      last_sync: "2025-07-13T12:44:59.567982Z",
    }),
    [comparisonData]
  );

  const barsDataIsLoading = false;
  const barsDataIsPending = false;
  const barsDataError = null;

  // Generate tabs from API data with comparison
  const trafficOverviewTabs = useMemo(() => {
    if (!comparisonData?.current || !comparisonData?.previous) {
      return [
        { id: 1, title: "Total Users", value: "0", changeValue: "+0.0%" },
        { id: 2, title: "New Users", value: "0", changeValue: "+0.0%" },
        { id: 3, title: "Sessions", value: "0", changeValue: "+0.0%" },
        { id: 4, title: "Active Users", value: "0", changeValue: "+0.0%" },
        { id: 5, title: "Views", value: "0", changeValue: "+0.0%" },
        { id: 6, title: "Event Count", value: "0", changeValue: "+0.0%" },
        { id: 7, title: "Conversions", value: "0", changeValue: "+0.0%" },
      ];
    }

    const currentMetrics = comparisonData.current.metrics;
    const previousMetrics = comparisonData.previous.metrics;

    const calculateChange = (current: number, previous: number): string => {
      if (previous === 0) {
        return current > 0 ? "+100%" : "0%";
      }
      const change = ((current - previous) / previous) * 100;
      const sign = change >= 0 ? "+" : "";
      return `${sign}${change.toFixed(1)}%`;
    };

    return [
      {
        id: 1,
        title: "Total Users",
        value: formatMetricValue(currentMetrics.total_users.total_value),
        changeValue: calculateChange(
          currentMetrics.total_users.total_value,
          previousMetrics.total_users.total_value
        ),
      },
      {
        id: 2,
        title: "New Users",
        value: formatMetricValue(currentMetrics.new_users.total_value),
        changeValue: calculateChange(
          currentMetrics.new_users.total_value,
          previousMetrics.new_users.total_value
        ),
      },
      {
        id: 3,
        title: "Sessions",
        value: formatMetricValue(currentMetrics.sessions.total_value),
        changeValue: calculateChange(
          currentMetrics.sessions.total_value,
          previousMetrics.sessions.total_value
        ),
      },
      {
        id: 4,
        title: "Active Users",
        value: formatMetricValue(currentMetrics.active_users.total_value),
        changeValue: calculateChange(
          currentMetrics.active_users.total_value,
          previousMetrics.active_users.total_value
        ),
      },
      {
        id: 5,
        title: "Views",
        value: formatMetricValue(currentMetrics.page_views.total_value),
        changeValue: calculateChange(
          currentMetrics.page_views.total_value,
          previousMetrics.page_views.total_value
        ),
      },
      {
        id: 6,
        title: "Event Count",
        value: formatMetricValue(currentMetrics.event_count.total_value),
        changeValue: calculateChange(
          currentMetrics.event_count.total_value,
          previousMetrics.event_count.total_value
        ),
      },
      {
        id: 7,
        title: "Conversions",
        value: formatMetricValue(currentMetrics.conversions.total_value),
        changeValue: calculateChange(
          currentMetrics.conversions.total_value,
          previousMetrics.conversions.total_value
        ),
      },
    ];
  }, [comparisonData]);

  const [activeTab, setActiveTab] = useState("Total Users");

  // Transform data for chart with comparison
  const chartData = useMemo(() => {
    if (!comparisonData?.current || !comparisonData?.previous) return null;

    const currentMetric = getMetricByName(comparisonData.current, activeTab);
    const previousMetric = getMetricByName(comparisonData.previous, activeTab);

    if (!currentMetric || !previousMetric) return null;

    return transformComparisonMetricToChartData(
      currentMetric,
      previousMetric,
      activeTab
    );
  }, [comparisonData, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (
    !trafficData &&
    !barsDataIsLoading &&
    !barsDataIsPending &&
    !barsDataError
  ) {
    return <NoData title="Audience Overview" />;
  }

  return (
    <Card className="space-y-4">
      <Title>Traffic Overview</Title>
      <DateRange />

      <div className="flex justify-between overflow-x-auto gap-1 h-fit text-nowrap">
        {trafficOverviewTabs.map(({ id, title, value, changeValue }) => (
          <CardTab
            key={id}
            title={title}
            value={value}
            changeValue={changeValue}
            className={`border-2 `}
            style={
              activeTab === title
                ? { borderColor: themeColor }
                : { borderColor: "transparent" }
            }
            onSelect={() => setActiveTab(title)}
          />
        ))}
      </div>
      <div>
        {barsDataIsLoading || barsDataIsPending ? (
          <TrafficOverViewSkeleton />
        ) : (
          chartData && <OrganicTraffic barsData={chartData} />
        )}
      </div>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link
          href={"/project/analytics-traffics/analytic-insight?tab=traffics"}
        >
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default TrafficOverview;
