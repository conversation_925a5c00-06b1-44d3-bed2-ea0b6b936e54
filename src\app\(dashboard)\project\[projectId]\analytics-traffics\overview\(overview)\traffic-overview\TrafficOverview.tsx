import React, { useState, useMemo } from "react";

/* ================================ SKELETON ================================ */
import Skeleton from "react-loading-skeleton";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import CardTab from "@/components/ui/card-tab/CardTab";
import OrganicTraffic from "./_components/OrganicTraffic";
import { Button } from "@/components/ui/button";
import DateRange from "../../../_components/date-range/DateRange";
import NoData from "../../../analytic-insight/_components/NoData";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ================================== UTILS ================================= */
import {
  transformMetricToChartData,
  getMetricByName,
  formatMetricValue,
  calculatePercentageChange,
} from "./utils/transformTrafficData";

const TrafficOverViewSkeleton = () => {
  return (
    <div className="w-[90%] mx-8 p-2 space-y-2 border-l border-b">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index}>
          <Skeleton width={80} height={15} />
          <Skeleton width={`${Math.floor(Math.random() * 101)}%`} />
        </div>
      ))}
    </div>
  );
};

/* ================================ API CALLS =============================== */
import useTrafficOverview from "./TrafficOverview.hook";
import Link from "next/link";

/* ========================================================================== */
const TrafficOverview = () => {
  /* ================================ CONSTANTS =============================== */
  const { themeColor } = useAppThemeColor();

  const {
    data: trafficData,
    isLoading: barsDataIsLoading,
    isPending: barsDataIsPending,
    error: barsDataError,
  } = useTrafficOverview();

  // Generate tabs from API data
  const trafficOverviewTabs = useMemo(() => {
    if (!trafficData?.data) {
      return [
        { id: 1, title: "Total Users", value: "0", changeValue: "+0.0" },
        { id: 2, title: "New Users", value: "0", changeValue: "+0.0" },
        { id: 3, title: "Sessions", value: "0", changeValue: "+0.0" },
        { id: 4, title: "Active Users", value: "0", changeValue: "+0.0" },
        { id: 5, title: "Views", value: "0", changeValue: "+0.0" },
        { id: 6, title: "Event Count", value: "0", changeValue: "+0.0" },
        { id: 7, title: "Conversions", value: "0", changeValue: "+0.0" },
      ];
    }

    const { metrics } = trafficData.data;
    return [
      {
        id: 1,
        title: "Total Users",
        value: formatMetricValue(metrics.total_users.total_value),
        changeValue: calculatePercentageChange(metrics.total_users.total_value),
      },
      {
        id: 2,
        title: "New Users",
        value: formatMetricValue(metrics.new_users.total_value),
        changeValue: calculatePercentageChange(metrics.new_users.total_value),
      },
      {
        id: 3,
        title: "Sessions",
        value: formatMetricValue(metrics.sessions.total_value),
        changeValue: calculatePercentageChange(metrics.sessions.total_value),
      },
      {
        id: 4,
        title: "Active Users",
        value: formatMetricValue(metrics.active_users.total_value),
        changeValue: calculatePercentageChange(
          metrics.active_users.total_value
        ),
      },
      {
        id: 5,
        title: "Views",
        value: formatMetricValue(metrics.page_views.total_value),
        changeValue: calculatePercentageChange(metrics.page_views.total_value),
      },
      {
        id: 6,
        title: "Event Count",
        value: formatMetricValue(metrics.event_count.total_value),
        changeValue: calculatePercentageChange(metrics.event_count.total_value),
      },
      {
        id: 7,
        title: "Conversions",
        value: formatMetricValue(metrics.conversions.total_value),
        changeValue: calculatePercentageChange(metrics.conversions.total_value),
      },
    ];
  }, [trafficData]);

  const [activeTab, setActiveTab] = useState(
    trafficOverviewTabs[0]?.title || ""
  );

  // Transform data for chart
  const chartData = useMemo(() => {
    if (!trafficData?.data) return null;

    const metric = getMetricByName(trafficData.data, activeTab);
    if (!metric) return null;

    return transformMetricToChartData(metric, activeTab);
  }, [trafficData, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (
    !trafficData &&
    !barsDataIsLoading &&
    !barsDataIsPending &&
    !barsDataError
  ) {
    return <NoData title="Audience Overview" />;
  }

  return (
    <Card className="space-y-4">
      <Title>Traffic Overview</Title>
      <DateRange />

      <div className="flex justify-between overflow-x-auto gap-1 h-fit text-nowrap">
        {trafficOverviewTabs.map(({ id, title, value, changeValue }) => (
          <CardTab
            key={id}
            title={title}
            value={value}
            changeValue={changeValue}
            className={`border-2 `}
            style={
              activeTab === title
                ? { borderColor: themeColor }
                : { borderColor: "transparent" }
            }
            onSelect={() => setActiveTab(title)}
          />
        ))}
      </div>
      <div>
        {barsDataIsLoading || barsDataIsPending ? (
          <TrafficOverViewSkeleton />
        ) : (
          chartData && <OrganicTraffic barsData={chartData} />
        )}
      </div>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link
          href={"/project/analytics-traffics/analytic-insight?tab=traffics"}
        >
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default TrafficOverview;
