import React, { useState, useRef } from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";
import abbreviateNumber from "@/utils/abbreviateNumber";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================== TYPES ================================= */
import type { THorizontalBar } from "./HorizontalBar.types";

/* ============================= REACT SKELETON ============================= */
import Skeleton from "react-loading-skeleton";

/* =============================== COMPONENTS =============================== */
import Card from "../card";

/* ========================================================================== */
/**
 * HorizontalBar - A responsive and animated horizontal stacked bar chart with tooltip.
 *
 * @param {string} label - The label displayed above the bar.
 * @param {TBar[]} bars - Array of bar segments with value, and color.
 * @param {number} totalValue - The total value to calculate each bar's percentage.
 * @param {string} [className] - Optional additional class names for styling.
 *
 * Features:
 * - Animates bar widths on scroll into view using Framer Motion.
 * - Shows a tooltip near cursor with segment values, formatted (e.g., 1.2K, 3.4M).
 * - Tooltip intelligently flips above cursor if near container bottom.
 * - Validates that sum of bar values does not exceed totalValue.
 *
 * Usage example:
 * ```tsx
 * <HorizontalBar
 *   label="Revenue Breakdown"
 *   totalValue={1000}
 *   bars={[
 *     { value: 300, color: "bg-blue-500" },
 *     { value: 500, color: "bg-green-500" },
 *     { value: 200, color: "bg-yellow-500" },
 *   ]}
 *   className="my-4"
 * />
 * ```
 */

const HorizontalBar = ({
  label,
  bars,
  totalValue,
  className,
  percentageLabel,
  isLoading,
}: THorizontalBar) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const containerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const offset = 12;
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [showTooltip, setShowTooltip] = useState(false);

  const calculatedTotal = bars?.reduce((sum, bar) => sum + bar.value, 0);

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */

  const handleMouseMove = (e: React.MouseEvent) => {
    const bounds = containerRef.current?.getBoundingClientRect();
    if (!bounds) return;

    setMousePos({
      x: e.clientX - bounds.left,
      y: e.clientY - bounds.top,
    });
  };

  const getTooltipPosition = () => {
    const containerBounds = containerRef.current?.getBoundingClientRect();
    const tooltipBounds = tooltipRef.current?.getBoundingClientRect();

    if (!containerBounds || !tooltipBounds) return { top: 0, left: 0 };

    const fitsBelow =
      mousePos.y + tooltipBounds.height + offset < containerBounds.height;

    const top = fitsBelow
      ? mousePos.y + offset
      : mousePos.y - tooltipBounds.height - offset;

    const left = mousePos.x + offset;

    return { top, left };
  };

  if (calculatedTotal && totalValue && calculatedTotal > totalValue)
    return (
      <span className="text-rose-500">
        total value exceeds sum of bar values
      </span>
    );

  const tooltipPos = getTooltipPosition();

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div
      ref={containerRef}
      className={cn("relative", className)}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <div className={`flex justify-between mb-2`}>
        {isLoading ? (
          <motion.div
            key={"loading"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <Skeleton height={10} width={100} />
          </motion.div>
        ) : (
          <motion.div
            key={"date"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <span className="text-secondary text-xs">{label}</span>
          </motion.div>
        )}
        <div className="text-secondary text-xs space-x-2">
          {isLoading ? (
            <motion.div
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <Skeleton height={10} width={50} />
            </motion.div>
          ) : (
            calculatedTotal &&
            totalValue && (
              <motion.div
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-x-1"
              >
                <span>{Math.ceil((calculatedTotal / totalValue) * 100)}%</span>
                <span>{percentageLabel}</span>
              </motion.div>
            )
          )}
        </div>
      </div>
      <div className="w-full h-4 border rounded-xs overflow-hidden flex gap-[1px]">
        {isLoading ? (
          <motion.div
            key={"loading"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="border"
          >
            {Array.from({ length: 7 }).map((_, i) => (
              <div className="flex gap-[1px]" key={i}>
                <div className="rounded-md w-[100px] relative">
                  <Skeleton className="-translate-y-2" height={"100%"} />
                </div>
                <div className="rounded-md w-[100px] relative">
                  <Skeleton className="-translate-y-2" height={"100%"} />
                </div>
              </div>
            ))}
          </motion.div>
        ) : (
          totalValue &&
          bars?.map(({ color, value }, index) => {
            const percentage = (value / totalValue) * 100;
            return (
              <motion.div
                key={index}
                className={`h-full ${color ?? "bg-gray-500"} rounded-xs`}
                initial={{ width: 0 }}
                whileInView={{ width: `${percentage}%` }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              />
            );
          })
        )}
      </div>

      {showTooltip && (
        <Card
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="space-y-3 border shadow-md w-fit absolute pointer-events-none z-10"
          ref={tooltipRef}
          style={{
            top: tooltipPos.top,
            left: tooltipPos.left,
          }}
        >
          {/* Tooltip title showing traffic source name */}
          <div className="text-xs font-bold text-center border-b border-gray-200 pb-2 mb-2">
            {label}
          </div>

          {/* Tooltip content showing period data */}
          {bars?.map(
            ({ color, value, actualValue, label: periodLabel }, index) => (
              <div key={index} className="flex items-center gap-2.5">
                <div className={`${color} rounded-full w-3 h-3`} />
                <span className="text-xs font-medium text-gray-600">
                  {periodLabel}:
                </span>
                <span className="text-xs font-extrabold">
                  {abbreviateNumber(actualValue ?? value)}
                </span>
              </div>
            )
          )}
        </Card>
      )}
    </div>
  );
};

export default HorizontalBar;
