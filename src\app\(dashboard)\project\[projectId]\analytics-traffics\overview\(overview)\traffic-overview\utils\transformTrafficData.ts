import type {
  TrafficOverviewData,
  BarsData,
  Metric,
} from "../../../../types/HorizontalBars.types";

// Color mapping for different traffic sources
const TRAFFIC_SOURCE_COLORS = {
  organic: "bg-green-500",
  paid: "bg-blue-500",
  direct: "bg-purple-500",
  social: "bg-pink-500",
  referral: "bg-orange-500",
  email: "bg-yellow-500",
  unassigned: "bg-gray-500",
} as const;

// Traffic source labels for display
const TRAFFIC_SOURCE_LABELS = {
  organic: "Organic",
  paid: "Paid",
  direct: "Direct",
  social: "Social",
  referral: "Referral",
  email: "Email",
  unassigned: "Unassigned",
} as const;

/**
 * Transform traffic overview data for a specific metric into chart format
 */
export const transformMetricToChartData = (
  metric: Metric,
  metricName: string
): BarsData => {
  const trafficSources = Object.entries(metric.traffic_sources)
    .filter(([_, source]) => source.value > 0) // Only include sources with data
    .map(([key, source]) => ({
      value: source.value,
      color: TRAFFIC_SOURCE_COLORS[key as keyof typeof TRAFFIC_SOURCE_COLORS],
    }));

  // If no traffic sources have data (like conversions), show a placeholder
  const finalTrafficSources =
    trafficSources.length > 0
      ? trafficSources
      : [{ value: 0, color: "bg-gray-300" }];

  return {
    maxValue: Math.max(metric.total_value, 1), // Ensure maxValue is at least 1 for display
    bars: [
      {
        label: metricName,
        barData: finalTrafficSources,
      },
    ],
  };
};

/**
 * Get metric data by metric name
 */
export const getMetricByName = (
  data: TrafficOverviewData,
  metricName: string
): Metric | null => {
  const metricMap: Record<string, keyof TrafficOverviewData["metrics"]> = {
    "Total Users": "total_users",
    "New Users": "new_users",
    Sessions: "sessions",
    "Active Users": "active_users",
    Views: "page_views",
    "Event Count": "event_count",
    Conversions: "conversions",
  };

  const metricKey = metricMap[metricName];
  return metricKey ? data.metrics[metricKey] : null;
};

/**
 * Format number for display (e.g., 1000 -> 1K)
 */
export const formatMetricValue = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
};

/**
 * Calculate percentage change (placeholder for future implementation)
 * This would require historical data to compare against
 */
export const calculatePercentageChange = (
  currentValue: number,
  previousValue?: number
): string => {
  if (!previousValue) {
    return "+0.0"; // Placeholder when no historical data
  }

  const change = ((currentValue - previousValue) / previousValue) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}`;
};
