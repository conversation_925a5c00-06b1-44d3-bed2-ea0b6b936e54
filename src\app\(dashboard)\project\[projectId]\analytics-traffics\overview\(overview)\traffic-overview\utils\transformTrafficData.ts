import type {
  TrafficOverviewData,
  BarsData,
  Metric,
  ComparisonTrafficOverviewData,
} from "../../../../types/HorizontalBars.types";

// Color mapping for different traffic sources
const TRAFFIC_SOURCE_COLORS = {
  organic: "bg-green-500",
  paid: "bg-blue-500",
  direct: "bg-purple-500",
  social: "bg-pink-500",
  referral: "bg-orange-500",
  email: "bg-yellow-500",
  unassigned: "bg-gray-500",
} as const;

// Traffic source labels for display
const TRAFFIC_SOURCE_LABELS = {
  organic: "Organic",
  paid: "Paid",
  direct: "Direct",
  social: "Social",
  referral: "Referral",
  email: "Email",
  unassigned: "Unassigned",
} as const;

/**
 * Transform traffic overview data for a specific metric into chart format
 * Each traffic source gets its own row with purple color
 */
export const transformMetricToChartData = (
  metric: Metric,
  metricName: string
): BarsData => {
  const trafficSourceBars = Object.entries(metric.traffic_sources)
    .filter(([_, source]) => source.value > 0) // Only include sources with data
    .map(([key, source]) => ({
      label: TRAFFIC_SOURCE_LABELS[key as keyof typeof TRAFFIC_SOURCE_LABELS],
      barData: [
        {
          value: source.value,
          color: "bg-purple-500", // All bars use purple color
        },
      ],
    }));

  // If no traffic sources have data (like conversions), show a placeholder
  const finalBars =
    trafficSourceBars.length > 0
      ? trafficSourceBars
      : [
          {
            label: "No Data",
            barData: [{ value: 0, color: "bg-gray-300" }],
          },
        ];

  return {
    maxValue: Math.max(metric.total_value, 1), // Ensure maxValue is at least 1 for display
    bars: finalBars,
  };
};

/**
 * Transform comparison traffic overview data for a specific metric into chart format
 * Shows previous period in yellow (#FFCD29) and current period in purple
 */
export const transformComparisonMetricToChartData = (
  currentMetric: Metric,
  previousMetric: Metric,
  metricName: string
): BarsData => {
  // Get all traffic sources that have data in either period
  const allSources = new Set([
    ...Object.keys(currentMetric.traffic_sources),
    ...Object.keys(previousMetric.traffic_sources),
  ]);

  const trafficSourceBars = Array.from(allSources)
    .filter((key) => {
      const currentValue =
        currentMetric.traffic_sources[
          key as keyof typeof currentMetric.traffic_sources
        ]?.value || 0;
      const previousValue =
        previousMetric.traffic_sources[
          key as keyof typeof previousMetric.traffic_sources
        ]?.value || 0;
      return currentValue > 0 || previousValue > 0;
    })
    .map((key) => {
      const currentValue =
        currentMetric.traffic_sources[
          key as keyof typeof currentMetric.traffic_sources
        ]?.value || 0;
      const previousValue =
        previousMetric.traffic_sources[
          key as keyof typeof previousMetric.traffic_sources
        ]?.value || 0;

      const sourceName =
        TRAFFIC_SOURCE_LABELS[key as keyof typeof TRAFFIC_SOURCE_LABELS];

      const barData = [];

      // Always add the previous period as the base (yellow)
      if (previousValue > 0) {
        barData.push({
          value: previousValue,
          color: "bg-[#FFCD29]", // Yellow for previous period
        });
      }

      // Add the difference (current - previous) in purple if current is higher
      if (currentValue > previousValue) {
        barData.push({
          value: currentValue - previousValue,
          color: "bg-purple-500", // Purple for the additional amount
        });
      }
      // If current is lower than previous, show only the current amount in purple
      else if (currentValue > 0 && currentValue < previousValue) {
        // Replace the previous bar with current value in purple
        barData.length = 0; // Clear previous
        barData.push({
          value: currentValue,
          color: "bg-purple-500", // Purple for current period
        });
        // Add the remaining previous amount in a lighter color to show the decrease
        barData.push({
          value: previousValue - currentValue,
          color: "bg-yellow-200", // Light yellow for the decreased amount
        });
      }
      // If only current value exists (no previous), show full current in purple
      else if (currentValue > 0 && previousValue === 0) {
        barData.push({
          value: currentValue,
          color: "bg-purple-500", // Purple for current period
        });
      }

      return {
        label: sourceName,
        barData,
      };
    });

  // If no traffic sources have data, show a placeholder
  const finalBars =
    trafficSourceBars.length > 0
      ? trafficSourceBars
      : [
          {
            label: "No Data",
            barData: [{ value: 0, color: "bg-gray-300" }],
          },
        ];

  // Use the maximum total value from both periods for proper scaling
  const maxValue = Math.max(
    currentMetric.total_value,
    previousMetric.total_value,
    1
  );

  return {
    maxValue,
    bars: finalBars,
  };
};

/**
 * Get metric data by metric name
 */
export const getMetricByName = (
  data: TrafficOverviewData,
  metricName: string
): Metric | null => {
  const metricMap: Record<string, keyof TrafficOverviewData["metrics"]> = {
    "Total Users": "total_users",
    "New Users": "new_users",
    Sessions: "sessions",
    "Active Users": "active_users",
    Views: "page_views",
    "Event Count": "event_count",
    Conversions: "conversions",
  };

  const metricKey = metricMap[metricName];
  return metricKey ? data.metrics[metricKey] : null;
};

/**
 * Format number for display (e.g., 1000 -> 1K)
 */
export const formatMetricValue = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
};

/**
 * Calculate percentage change (placeholder for future implementation)
 * This would require historical data to compare against
 */
export const calculatePercentageChange = (
  currentValue: number,
  previousValue?: number
): string => {
  if (!previousValue) {
    return "+0.0"; // Placeholder when no historical data
  }

  const change = ((currentValue - previousValue) / previousValue) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}`;
};
